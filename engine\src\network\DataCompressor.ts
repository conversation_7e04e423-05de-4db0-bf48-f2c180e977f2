/**
 * 数据压缩器
 * 负责压缩和解压缩网络传输的数据
 */
import { Debug } from '../utils/Debug';

/**
 * 压缩算法
 */
export enum CompressionAlgorithm {
  /** 无压缩 */
  NONE = 'none',
  /** LZ字符串压缩 */
  LZ_STRING = 'lz_string',
  /** MessagePack */
  MSGPACK = 'msgpack',
  /** CBOR (Concise Binary Object Representation) */
  CBOR = 'cbor',
  /** Brotli压缩 */
  BROTLI = 'brotli',
  /** Deflate压缩 */
  DEFLATE = 'deflate',
  /** 增量压缩 */
  INCREMENTAL = 'incremental',
  /** 自定义压缩 */
  CUSTOM = 'custom',
}

/**
 * 压缩级别
 */
export enum CompressionLevel {
  /** 无压缩 */
  NONE = 0,
  /** 低压缩 */
  LOW = 1,
  /** 中等压缩 */
  MEDIUM = 2,
  /** 高压缩 */
  HIGH = 3,
  /** 最高压缩 */
  HIGHEST = 4,
}

/**
 * 增量压缩选项
 */
export interface IncrementalCompressionOptions {
  /** 是否启用增量压缩 */
  enabled?: boolean;
  /** 最大递归深度 */
  maxDepth?: number;
  /** 是否包含路径信息 */
  includePathInfo?: boolean;
  /** 是否使用二进制差异 */
  useBinaryDiff?: boolean;
  /** 是否压缩增量数据 */
  compressIncrementalData?: boolean;
  /** 是否使用字段过滤 */
  useFieldFiltering?: boolean;
  /** 要包含的字段列表 */
  includedFields?: string[];
  /** 要排除的字段列表 */
  excludedFields?: string[];
}

/**
 * 压缩选项
 */
export interface CompressionOptions {
  /** 压缩算法 */
  algorithm?: CompressionAlgorithm;
  /** 压缩级别 */
  level?: CompressionLevel;
  /** 是否启用自适应压缩 */
  adaptive?: boolean;
  /** 最小压缩大小（字节） */
  minSize?: number;
  /** 是否使用二进制格式 */
  useBinaryFormat?: boolean;
  /** 是否使用类型化数组优化 */
  useTypedArrayOptimization?: boolean;
  /** 是否使用字典压缩 */
  useDictionaryCompression?: boolean;
  /** 压缩字典 */
  compressionDictionary?: Uint8Array;
  /** 增量压缩选项 */
  incremental?: IncrementalCompressionOptions;
  /** 自定义压缩函数 */
  customCompressFunction?: (data: any) => string | Uint8Array;
  /** 自定义解压缩函数 */
  customDecompressFunction?: (data: string | Uint8Array) => any;
}

/**
 * 增量压缩结果
 */
export interface IncrementalCompressionResult {
  /** 是否为增量数据 */
  isIncremental: boolean;
  /** 增量版本 */
  version: number;
  /** 是否为完整数据 */
  isComplete?: boolean;
  /** 是否为空增量 */
  isEmpty?: boolean;
  /** 变更路径 */
  paths?: string[];
  /** 变更字段数量 */
  changedFieldsCount?: number;
  /** 增量数据大小（字节） */
  incrementalSize?: number;
  /** 完整数据大小（字节） */
  fullSize?: number;
  /** 节省的字节数 */
  savedBytes?: number;
  /** 节省百分比 */
  savingsPercentage?: number;
}

/**
 * 压缩结果
 */
export interface CompressionResult {
  /** 压缩后的数据 */
  data: string | Uint8Array;
  /** 原始大小（字节） */
  originalSize: number;
  /** 压缩后大小（字节） */
  compressedSize: number;
  /** 压缩比率（0-1，越小表示压缩效果越好） */
  ratio: number;
  /** 压缩算法 */
  algorithm: CompressionAlgorithm;
  /** 压缩级别 */
  level: CompressionLevel;
  /** 压缩时间（毫秒） */
  time: number;
  /** 是否使用了二进制格式 */
  isBinary?: boolean;
  /** 是否使用了字典压缩 */
  usedDictionary?: boolean;
  /** 增量压缩结果 */
  incremental?: IncrementalCompressionResult;
}

/**
 * 数据压缩器
 * 负责压缩和解压缩网络传输的数据
 */
export class DataCompressor {
  /** 配置 */
  private options: Required<CompressionOptions>;

  /** 压缩统计信息 */
  private stats: {
    /** 总压缩次数 */
    compressionCount: number;
    /** 总解压缩次数 */
    decompressionCount: number;
    /** 总原始大小（字节） */
    totalOriginalSize: number;
    /** 总压缩后大小（字节） */
    totalCompressedSize: number;
    /** 总压缩时间（毫秒） */
    totalCompressionTime: number;
    /** 总解压缩时间（毫秒） */
    totalDecompressionTime: number;
    /** 增量压缩次数 */
    incrementalCompressionCount: number;
    /** 增量压缩节省的字节数 */
    incrementalSavedBytes: number;
    /** 自适应算法切换次数 */
    adaptiveAlgorithmSwitchCount: number;
  };

  /** 压缩字典 */
  private compressionDictionary?: Uint8Array;

  /** 上次压缩的数据缓存 */
  private lastCompressedDataCache: Map<string, any> = new Map();

  /** 是否已加载依赖 */
  private dependenciesLoaded: boolean = false;

  /** 压缩库引用 */
  private compressionLibs: {
    lzString?: any;
    msgpack?: any;
    cbor?: any;
    brotli?: any;
    deflate?: any;
  } = {};

  /**
   * 创建数据压缩器
   * @param options 压缩选项
   */
  constructor(options: CompressionOptions = {}) {
    // 默认增量压缩选项
    const defaultIncrementalOptions: Required<IncrementalCompressionOptions> = {
      enabled: true,
      maxDepth: 10,
      includePathInfo: true,
      useBinaryDiff: false,
      compressIncrementalData: true,
      useFieldFiltering: false,
      includedFields: [],
      excludedFields: []
    };

    // 默认配置
    this.options = {
      algorithm: CompressionAlgorithm.LZ_STRING,
      level: CompressionLevel.MEDIUM,
      adaptive: true,
      minSize: 100, // 小于100字节的数据不压缩
      useBinaryFormat: false,
      useTypedArrayOptimization: true,
      useDictionaryCompression: false,
      compressionDictionary: undefined,
      incremental: defaultIncrementalOptions,
      customCompressFunction: undefined,
      customDecompressFunction: undefined,
      ...options,
      // 确保增量选项被正确合并
      incremental: {
        ...defaultIncrementalOptions,
        ...(options.incremental || {})
      }
    };

    // 初始化统计信息
    this.stats = {
      compressionCount: 0,
      decompressionCount: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalCompressionTime: 0,
      totalDecompressionTime: 0,
      incrementalCompressionCount: 0,
      incrementalSavedBytes: 0,
      adaptiveAlgorithmSwitchCount: 0
    };

    // 设置压缩字典
    if (this.options.useDictionaryCompression && this.options.compressionDictionary) {
      this.compressionDictionary = this.options.compressionDictionary;
    }

    // 加载依赖
    this.loadDependencies();
  }

  /**
   * 加载依赖
   */
  private async loadDependencies(): Promise<void> {
    if (this.dependenciesLoaded) {
      return;
    }

    try {
      // 根据配置的算法加载相应的依赖
      switch (this.options.algorithm) {
        case CompressionAlgorithm.LZ_STRING:
          // 动态导入LZ-String库
          try {
            const lzString = await import('lz-string');
            this.compressionLibs.lzString = lzString.default || lzString;
            Debug.log('DataCompressor', '成功加载LZ-String库');
          } catch (error) {
            Debug.warn('DataCompressor', '无法加载LZ-String库，将使用内置的简单实现:', error);
          }
          break;

        case CompressionAlgorithm.MSGPACK:
          // 动态导入MessagePack库
          try {
            const msgpack = await import('@msgpack/msgpack');
            this.compressionLibs.msgpack = msgpack;
            Debug.log('DataCompressor', '成功加载MessagePack库');
          } catch (error) {
            Debug.warn('DataCompressor', '无法加载MessagePack库，将使用内置的简单实现:', error);
          }
          break;

        case CompressionAlgorithm.CBOR:
          // 动态导入CBOR库
          try {
            const cbor = await import('cbor-web');
            this.compressionLibs.cbor = cbor;
            Debug.log('DataCompressor', '成功加载CBOR库');
          } catch (error) {
            Debug.warn('DataCompressor', '无法加载CBOR库，将使用内置的简单实现:', error);
          }
          break;

        case CompressionAlgorithm.BROTLI:
          // 在浏览器环境中，可以使用CompressionStream API
          if (typeof CompressionStream !== 'undefined') {
            Debug.log('DataCompressor', '使用浏览器内置的CompressionStream API');
          } else {
            Debug.warn('DataCompressor', '当前环境不支持CompressionStream API，将使用内置的简单实现');
          }
          break;

        case CompressionAlgorithm.DEFLATE:
          // 在浏览器环境中，可以使用CompressionStream API
          if (typeof CompressionStream !== 'undefined') {
            Debug.log('DataCompressor', '使用浏览器内置的CompressionStream API');
          } else {
            Debug.warn('DataCompressor', '当前环境不支持CompressionStream API，将使用内置的简单实现');
          }
          break;
      }

      this.dependenciesLoaded = true;
    } catch (error) {
      Debug.error('DataCompressor', '加载依赖失败:', error);
    }
  }

  /**
   * 创建增量数据
   * @param newData 新数据
   * @param oldData 旧数据
   * @returns 增量数据
   */
  public async createIncrementalData(newData: any, oldData: any): Promise<any> {
    if (!this.options.incremental.enabled) {
      return newData;
    }

    // 开始计时
    const startTime = performance.now();

    // 创建增量对象
    const incremental: any = {
      __incremental: true,
      __version: 2, // 增量格式版本
      __timestamp: Date.now()
    };

    // 如果是全新数据，直接返回完整数据
    if (!oldData) {
      incremental.__complete = true;
      incremental.__data = newData;
      return incremental;
    }

    // 递归比较并创建增量
    const changes = this.createDeepIncrementalChanges(
      newData,
      oldData,
      '',
      0,
      this.options.incremental.maxDepth
    );

    // 如果没有变化，返回空增量
    if (Object.keys(changes).length === 0) {
      incremental.__empty = true;
      return incremental;
    }

    // 添加变更数据
    incremental.__data = changes;

    // 如果需要包含路径信息
    if (this.options.incremental.includePathInfo) {
      // 收集所有变更路径
      const paths = this.collectChangePaths(changes);
      if (paths.length > 0) {
        incremental.__paths = paths;
      }
    }

    // 计算增量大小和完整数据大小
    const incrementalSize = new TextEncoder().encode(JSON.stringify(incremental)).length;
    const fullSize = new TextEncoder().encode(JSON.stringify(newData)).length;

    // 计算节省的字节数和百分比
    const savedBytes = fullSize - incrementalSize;
    const savingsPercentage = (savedBytes / fullSize) * 100;

    // 更新统计信息
    this.stats.incrementalCompressionCount++;
    this.stats.incrementalSavedBytes += savedBytes;

    // 如果增量数据比完整数据大，则使用完整数据
    if (incrementalSize >= fullSize) {
      incremental.__complete = true;
      incremental.__data = newData;
      return incremental;
    }

    // 添加增量压缩结果信息
    incremental.__incrementalSize = incrementalSize;
    incremental.__fullSize = fullSize;
    incremental.__savedBytes = savedBytes;
    incremental.__savingsPercentage = savingsPercentage;
    incremental.__changedFieldsCount = Object.keys(changes).length;

    // 如果需要压缩增量数据
    if (this.options.incremental.compressIncrementalData) {
      // 压缩增量数据
      const compressedData = await this.compressIncrementalData(incremental.__data);
      incremental.__data = compressedData;
      incremental.__compressed = true;
    }

    return incremental;
  }

  /**
   * 应用增量数据
   * @param incrementalData 增量数据
   * @param currentData 当前数据
   * @returns 更新后的数据
   */
  public async applyIncrementalData(incrementalData: any, currentData: any): Promise<any> {
    // 如果不是增量数据，直接返回
    if (!incrementalData || !incrementalData.__incremental) {
      return incrementalData;
    }

    // 检查增量版本
    const version = incrementalData.__version || 1;

    // 如果是完整数据，直接使用
    if (incrementalData.__complete) {
      return incrementalData.__data || incrementalData;
    }

    // 如果是空增量，保持当前状态
    if (incrementalData.__empty) {
      return currentData;
    }

    // 获取增量数据
    let incrementalChanges = incrementalData.__data;

    // 如果增量数据被压缩，先解压缩
    if (incrementalData.__compressed) {
      incrementalChanges = await this.decompressIncrementalData(incrementalChanges);
    }

    // 根据版本应用增量
    let newData;
    if (version === 1) {
      // 版本1：简单属性覆盖
      newData = { ...currentData };

      for (const key in incrementalChanges) {
        newData[key] = incrementalChanges[key];
      }
    } else {
      // 版本2：深度增量
      newData = { ...currentData };

      // 应用深度增量变更
      this.applyDeepIncrementalChanges(newData, incrementalChanges);
    }

    return newData;
  }

  /**
   * 创建深度增量变更
   * @param newData 新数据
   * @param oldData 旧数据
   * @param path 当前路径
   * @param depth 当前深度
   * @param maxDepth 最大深度
   * @returns 增量变更
   */
  private createDeepIncrementalChanges(
    newData: any,
    oldData: any,
    path: string,
    depth: number,
    maxDepth: number
  ): any {
    // 如果超过最大深度，则返回完整数据
    if (depth >= maxDepth) {
      return newData;
    }

    // 如果新数据或旧数据为null或undefined，则直接比较
    if (newData === null || newData === undefined || oldData === null || oldData === undefined) {
      return newData !== oldData ? newData : {};
    }

    // 如果类型不同，则返回新数据
    if (typeof newData !== typeof oldData) {
      return newData;
    }

    // 如果是基本类型，则直接比较
    if (typeof newData !== 'object') {
      return newData !== oldData ? newData : {};
    }

    // 如果是数组，则比较数组
    if (Array.isArray(newData)) {
      // 如果旧数据不是数组，则返回新数组
      if (!Array.isArray(oldData)) {
        return newData;
      }

      // 如果数组长度不同，则返回新数组
      if (newData.length !== oldData.length) {
        return newData;
      }

      // 比较数组元素
      const changes: any = {};
      let hasChanges = false;

      for (let i = 0; i < newData.length; i++) {
        const elementChanges = this.createDeepIncrementalChanges(
          newData[i],
          oldData[i],
          `${path}[${i}]`,
          depth + 1,
          maxDepth
        );

        if (Object.keys(elementChanges).length > 0) {
          changes[i] = elementChanges;
          hasChanges = true;
        }
      }

      // 如果有变化，则返回变化
      if (hasChanges) {
        return { __array: true, __changes: changes };
      }

      return {};
    }

    // 如果是对象，则比较对象
    const changes: any = {};
    let hasChanges = false;

    // 检查新增和修改的属性
    for (const key in newData) {
      // 如果使用字段过滤，则检查是否应该包含该字段
      if (this.options.incremental.useFieldFiltering) {
        if (this.options.incremental.includedFields.length > 0 &&
            !this.options.incremental.includedFields.includes(key)) {
          continue;
        }

        if (this.options.incremental.excludedFields.length > 0 &&
            this.options.incremental.excludedFields.includes(key)) {
          continue;
        }
      }

      // 如果属性不在旧数据中，则添加
      if (!(key in oldData)) {
        changes[key] = newData[key];
        hasChanges = true;
        continue;
      }

      // 递归比较属性值
      const propertyChanges = this.createDeepIncrementalChanges(
        newData[key],
        oldData[key],
        path ? `${path}.${key}` : key,
        depth + 1,
        maxDepth
      );

      // 如果有变化，则添加
      if (Object.keys(propertyChanges).length > 0) {
        changes[key] = propertyChanges;
        hasChanges = true;
      }
    }

    // 检查删除的属性
    for (const key in oldData) {
      if (!(key in newData)) {
        changes[key] = { __deleted: true };
        hasChanges = true;
      }
    }

    // 如果没有变化，返回空对象
    return hasChanges ? changes : {};
  }

  /**
   * 应用深度增量变更
   * @param target 目标对象
   * @param changes 变更对象
   */
  private applyDeepIncrementalChanges(target: any, changes: any): void {
    for (const key in changes) {
      // 处理删除标记
      if (changes[key] && typeof changes[key] === 'object' && changes[key].__deleted) {
        delete target[key];
        continue;
      }

      // 处理数组变更
      if (changes[key] && typeof changes[key] === 'object' && changes[key].__array) {
        // 确保目标是数组
        if (!Array.isArray(target[key])) {
          target[key] = [];
        }

        // 应用数组变更
        const arrayChanges = changes[key].__changes;
        for (const index in arrayChanges) {
          const i = parseInt(index, 10);

          // 确保数组长度足够
          while (target[key].length <= i) {
            target[key].push(undefined);
          }

          // 如果是对象变更，则递归应用
          if (typeof arrayChanges[index] === 'object' && !Array.isArray(arrayChanges[index]) &&
              Object.keys(arrayChanges[index]).length > 0) {
            // 如果目标元素不存在，则创建
            if (target[key][i] === undefined) {
              target[key][i] = {};
            }

            // 递归应用变更
            this.applyDeepIncrementalChanges(target[key][i], arrayChanges[index]);
          } else {
            // 直接替换
            target[key][i] = arrayChanges[index];
          }
        }

        continue;
      }

      // 处理对象变更
      if (changes[key] && typeof changes[key] === 'object' && Object.keys(changes[key]).length > 0 &&
          target[key] && typeof target[key] === 'object') {
        // 递归应用变更
        this.applyDeepIncrementalChanges(target[key], changes[key]);
      } else {
        // 直接替换
        target[key] = changes[key];
      }
    }
  }

  /**
   * 收集变更路径
   * @param changes 变更对象
   * @param basePath 基础路径
   * @returns 变更路径列表
   */
  private collectChangePaths(changes: any, basePath: string = ''): string[] {
    const paths: string[] = [];

    for (const key in changes) {
      const currentPath = basePath ? `${basePath}.${key}` : key;

      // 添加当前路径
      paths.push(currentPath);

      // 如果是对象且不是特殊标记，则递归收集
      if (changes[key] && typeof changes[key] === 'object' && !changes[key].__deleted) {
        // 处理数组变更
        if (changes[key].__array) {
          const arrayChanges = changes[key].__changes;
          for (const index in arrayChanges) {
            const arrayPath = `${currentPath}[${index}]`;
            paths.push(arrayPath);

            // 递归收集数组元素的变更路径
            if (arrayChanges[index] && typeof arrayChanges[index] === 'object') {
              const elementPaths = this.collectChangePaths(arrayChanges[index], arrayPath);
              paths.push(...elementPaths);
            }
          }
        } else {
          // 递归收集对象属性的变更路径
          const propertyPaths = this.collectChangePaths(changes[key], currentPath);
          paths.push(...propertyPaths);
        }
      }
    }

    return paths;
  }

  /**
   * 压缩增量数据
   * @param data 增量数据
   * @returns 压缩后的数据
   */
  private async compressIncrementalData(data: any): Promise<any> {
    // 使用当前配置的压缩算法压缩数据
    const result = await this.compress(data, {
      algorithm: this.options.algorithm,
      level: this.options.level
    });

    return result.data;
  }

  /**
   * 解压缩增量数据
   * @param data 压缩后的增量数据
   * @returns 解压缩后的数据
   */
  private async decompressIncrementalData(data: any): Promise<any> {
    // 使用当前配置的压缩算法解压缩数据
    return await this.decompress(data, this.options.algorithm, this.options.level);
  }

  /**
   * 压缩数据
   * @param data 要压缩的数据
   * @param options 压缩选项（可选，覆盖默认选项）
   * @returns 压缩结果
   */
  public async compress(data: any, options?: Partial<CompressionOptions>): Promise<CompressionResult> {
    // 合并选项
    const mergedOptions: Required<CompressionOptions> = {
      ...this.options,
      ...options,
      // 确保增量选项被正确合并
      incremental: {
        ...this.options.incremental,
        ...(options?.incremental || {})
      }
    };

    // 检查是否应该使用增量压缩
    if (mergedOptions.algorithm === CompressionAlgorithm.INCREMENTAL) {
      // 获取上次压缩的数据
      const cacheKey = typeof data === 'string' ? data : JSON.stringify(data);
      const lastData = this.lastCompressedDataCache.get(cacheKey);

      // 创建增量数据
      const incrementalData = await this.createIncrementalData(data, lastData);

      // 更新缓存
      this.lastCompressedDataCache.set(cacheKey, data);

      // 计算增量大小
      const incrementalSize = typeof incrementalData === 'string'
        ? new TextEncoder().encode(incrementalData).length
        : new TextEncoder().encode(JSON.stringify(incrementalData)).length;

      // 计算原始大小
      const originalSize = typeof data === 'string'
        ? new TextEncoder().encode(data).length
        : new TextEncoder().encode(JSON.stringify(data)).length;

      // 创建增量压缩结果
      const incrementalResult: IncrementalCompressionResult = {
        isIncremental: true,
        version: 2,
        isComplete: incrementalData.__complete || false,
        isEmpty: incrementalData.__empty || false,
        changedFieldsCount: incrementalData.__changedFieldsCount || 0,
        incrementalSize: incrementalData.__incrementalSize || incrementalSize,
        fullSize: incrementalData.__fullSize || originalSize,
        savedBytes: incrementalData.__savedBytes || (originalSize - incrementalSize),
        savingsPercentage: incrementalData.__savingsPercentage || ((originalSize - incrementalSize) / originalSize * 100)
      };

      // 返回压缩结果
      return {
        data: incrementalData,
        originalSize,
        compressedSize: incrementalSize,
        ratio: incrementalSize / originalSize,
        algorithm: CompressionAlgorithm.INCREMENTAL,
        level: mergedOptions.level,
        time: 0,
        incremental: incrementalResult
      };
    }

    // 将数据转换为适当的格式
    let originalData: string | Uint8Array;
    let originalSize: number;

    if (mergedOptions.useBinaryFormat && !(typeof data === 'string')) {
      // 如果使用二进制格式，则转换为二进制
      if (mergedOptions.useTypedArrayOptimization && this.canUseTypedArray(data)) {
        // 使用类型化数组优化
        originalData = this.convertToTypedArray(data);
      } else {
        // 使用标准二进制格式
        originalData = this.convertToBinary(data);
      }
      originalSize = originalData instanceof Uint8Array ? originalData.byteLength : new TextEncoder().encode(originalData).length;
    } else {
      // 使用JSON字符串
      originalData = typeof data === 'string' ? data : JSON.stringify(data);
      originalSize = new TextEncoder().encode(originalData).length;
    }

    // 如果数据小于最小压缩大小，则不压缩
    if (originalSize < mergedOptions.minSize) {
      return {
        data: originalData,
        originalSize,
        compressedSize: originalSize,
        ratio: 1,
        algorithm: CompressionAlgorithm.NONE,
        level: CompressionLevel.NONE,
        time: 0,
        isBinary: originalData instanceof Uint8Array
      };
    }

    // 如果启用自适应压缩，则根据数据大小选择算法和级别
    const oldAlgorithm = mergedOptions.algorithm;
    const oldLevel = mergedOptions.level;

    if (mergedOptions.adaptive) {
      this.selectAdaptiveCompression(originalSize, mergedOptions);

      // 如果算法或级别发生变化，记录切换次数
      if (oldAlgorithm !== mergedOptions.algorithm || oldLevel !== mergedOptions.level) {
        this.stats.adaptiveAlgorithmSwitchCount++;
      }
    }

    // 开始计时
    const startTime = performance.now();

    // 根据算法压缩数据
    let compressedData: string | Uint8Array;
    let usedDictionary = false;

    try {
      switch (mergedOptions.algorithm) {
        case CompressionAlgorithm.NONE:
          compressedData = originalData;
          break;

        case CompressionAlgorithm.LZ_STRING:
          compressedData = this.compressWithLZString(
            typeof originalData === 'string' ? originalData : new TextDecoder().decode(originalData),
            mergedOptions.level
          );
          break;

        case CompressionAlgorithm.MSGPACK:
          compressedData = this.compressWithMessagePack(data, mergedOptions.level);
          break;

        case CompressionAlgorithm.CBOR:
          compressedData = this.compressWithCBOR(data, mergedOptions.level);
          break;

        case CompressionAlgorithm.BROTLI:
          compressedData = await this.compressWithBrotli(
            typeof originalData === 'string' ? new TextEncoder().encode(originalData) : originalData,
            mergedOptions.level
          );
          break;

        case CompressionAlgorithm.DEFLATE:
          compressedData = await this.compressWithDeflate(
            typeof originalData === 'string' ? new TextEncoder().encode(originalData) : originalData,
            mergedOptions.level
          );
          break;

        case CompressionAlgorithm.CUSTOM:
          if (mergedOptions.customCompressFunction) {
            compressedData = mergedOptions.customCompressFunction(data);
          } else {
            throw new Error('Custom compression function is not defined');
          }
          break;

        default:
          compressedData = originalData;
          break;
      }

      // 如果启用了字典压缩，则使用字典压缩
      if (mergedOptions.useDictionaryCompression && this.compressionDictionary) {
        compressedData = this.compressWithDictionary(
          typeof compressedData === 'string' ? new TextEncoder().encode(compressedData) : compressedData,
          this.compressionDictionary
        );
        usedDictionary = true;
      }
    } catch (error) {
      Debug.error('DataCompressor', 'Compression error:', error);

      // 如果压缩失败，则返回原始数据
      compressedData = originalData;
      mergedOptions.algorithm = CompressionAlgorithm.NONE;
      mergedOptions.level = CompressionLevel.NONE;
    }

    // 计算压缩后大小
    const compressedSize = typeof compressedData === 'string'
      ? new TextEncoder().encode(compressedData).length
      : compressedData.byteLength;

    // 计算压缩比率
    const ratio = compressedSize / originalSize;

    // 计算压缩时间
    const endTime = performance.now();
    const compressionTime = endTime - startTime;

    // 更新统计信息
    this.stats.compressionCount++;
    this.stats.totalOriginalSize += originalSize;
    this.stats.totalCompressedSize += compressedSize;
    this.stats.totalCompressionTime += compressionTime;

    // 返回压缩结果
    return {
      data: compressedData,
      originalSize,
      compressedSize,
      ratio,
      algorithm: mergedOptions.algorithm,
      level: mergedOptions.level,
      time: compressionTime,
      isBinary: compressedData instanceof Uint8Array,
      usedDictionary
    };
  }

  /**
   * 检查是否可以使用类型化数组优化
   * @param data 数据
   * @returns 是否可以使用类型化数组优化
   */
  private canUseTypedArray(data: any): boolean {
    // 检查数据是否包含数值数组
    if (Array.isArray(data) && data.every(item => typeof item === 'number')) {
      return true;
    }

    // 检查数据是否包含向量或矩阵
    if (data && typeof data === 'object') {
      // 检查是否是向量
      if ('x' in data && 'y' in data && 'z' in data &&
          typeof data.x === 'number' && typeof data.y === 'number' && typeof data.z === 'number') {
        return true;
      }

      // 检查是否是四元数
      if ('x' in data && 'y' in data && 'z' in data && 'w' in data &&
          typeof data.x === 'number' && typeof data.y === 'number' &&
          typeof data.z === 'number' && typeof data.w === 'number') {
        return true;
      }

      // 检查是否是矩阵
      if ('elements' in data && Array.isArray(data.elements) &&
          data.elements.every((item: any) => typeof item === 'number')) {
        return true;
      }
    }

    return false;
  }

  /**
   * 将数据转换为类型化数组
   * @param data 数据
   * @returns 类型化数组
   */
  private convertToTypedArray(data: any): Uint8Array {
    // 如果是数值数组，则直接转换为Float32Array
    if (Array.isArray(data) && data.every(item => typeof item === 'number')) {
      const float32Array = new Float32Array(data);
      return new Uint8Array(float32Array.buffer);
    }

    // 如果是向量，则转换为Float32Array
    if (data && typeof data === 'object' && 'x' in data && 'y' in data && 'z' in data) {
      const float32Array = new Float32Array(data.w !== undefined ? 4 : 3);
      float32Array[0] = data.x;
      float32Array[1] = data.y;
      float32Array[2] = data.z;
      if (data.w !== undefined) {
        float32Array[3] = data.w;
      }
      return new Uint8Array(float32Array.buffer);
    }

    // 如果是矩阵，则转换为Float32Array
    if (data && typeof data === 'object' && 'elements' in data && Array.isArray(data.elements)) {
      const float32Array = new Float32Array(data.elements);
      return new Uint8Array(float32Array.buffer);
    }

    // 默认转换为JSON
    return this.convertToBinary(data);
  }

  /**
   * 将数据转换为二进制
   * @param data 数据
   * @returns 二进制数据
   */
  private convertToBinary(data: any): Uint8Array {
    // 将数据转换为JSON字符串
    const jsonString = JSON.stringify(data);

    // 转换为Uint8Array
    const encoder = new TextEncoder();
    return encoder.encode(jsonString);
  }

  /**
   * 解压缩数据
   * @param compressedData 压缩后的数据
   * @param algorithm 压缩算法
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  public async decompress(
    compressedData: string | Uint8Array,
    algorithm: CompressionAlgorithm = this.options.algorithm,
    level: CompressionLevel = this.options.level
  ): Promise<any> {
    // 检查是否是增量数据
    if (algorithm === CompressionAlgorithm.INCREMENTAL) {
      // 如果是增量数据，则应用增量
      if (typeof compressedData === 'object' && compressedData !== null && '__incremental' in compressedData) {
        // 获取上次压缩的数据
        const cacheKey = JSON.stringify(compressedData);
        const currentData = this.lastCompressedDataCache.get(cacheKey) || {};

        // 应用增量数据
        return await this.applyIncrementalData(compressedData, currentData);
      }

      // 如果不是增量数据，则直接返回
      return compressedData;
    }

    // 如果没有压缩，则直接返回
    if (algorithm === CompressionAlgorithm.NONE) {
      try {
        return typeof compressedData === 'string'
          ? (compressedData.startsWith('{') || compressedData.startsWith('[')
            ? JSON.parse(compressedData)
            : compressedData)
          : compressedData;
      } catch (error) {
        return compressedData;
      }
    }

    // 开始计时
    const startTime = performance.now();

    // 如果启用了字典压缩，则先解压缩字典
    if (this.options.useDictionaryCompression && this.compressionDictionary && compressedData instanceof Uint8Array) {
      try {
        compressedData = this.decompressWithDictionary(compressedData, this.compressionDictionary);
      } catch (error) {
        Debug.warn('DataCompressor', '字典解压缩失败:', error);
      }
    }

    // 根据算法解压缩数据
    let decompressedData: any;

    try {
      switch (algorithm) {
        case CompressionAlgorithm.LZ_STRING:
          decompressedData = this.decompressWithLZString(compressedData as string, level);
          break;

        case CompressionAlgorithm.MSGPACK:
          decompressedData = this.decompressWithMessagePack(compressedData, level);
          break;

        case CompressionAlgorithm.CBOR:
          decompressedData = this.decompressWithCBOR(compressedData as Uint8Array, level);
          break;

        case CompressionAlgorithm.BROTLI:
          decompressedData = await this.decompressWithBrotli(compressedData as Uint8Array, level);
          break;

        case CompressionAlgorithm.DEFLATE:
          decompressedData = await this.decompressWithDeflate(compressedData as Uint8Array, level);
          break;

        case CompressionAlgorithm.CUSTOM:
          if (this.options.customDecompressFunction) {
            decompressedData = this.options.customDecompressFunction(compressedData);
          } else {
            throw new Error('Custom decompression function is not defined');
          }
          break;

        default:
          decompressedData = compressedData;
          break;
      }

      // 如果解压缩后的数据是二进制，则尝试转换为对象
      if (decompressedData instanceof Uint8Array) {
        try {
          const decoder = new TextDecoder();
          const jsonString = decoder.decode(decompressedData);

          if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
            decompressedData = JSON.parse(jsonString);
          }
        } catch (error) {
          // 如果转换失败，则保持原样
        }
      }

      // 如果解压缩后的数据是JSON字符串，则解析为对象
      if (typeof decompressedData === 'string' && (decompressedData.startsWith('{') || decompressedData.startsWith('['))) {
        decompressedData = JSON.parse(decompressedData);
      }
    } catch (error) {
      Debug.error('DataCompressor', 'Decompression error:', error);

      // 如果解压缩失败，则返回原始数据
      decompressedData = compressedData;
    }

    // 计算解压缩时间
    const endTime = performance.now();
    const decompressionTime = endTime - startTime;

    // 更新统计信息
    this.stats.decompressionCount++;
    this.stats.totalDecompressionTime += decompressionTime;

    return decompressedData;
  }

  /**
   * 使用Brotli解压缩
   * @param data 要解压缩的数据
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  private async decompressWithBrotli(data: Uint8Array, _level: CompressionLevel): Promise<Uint8Array> {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果浏览器支持DecompressionStream，则使用DecompressionStream
    if (typeof DecompressionStream !== 'undefined') {
      try {
        // 创建解压缩流
        const ds = new DecompressionStream('gzip' as any); // 使用 gzip 作为 Brotli 的替代
        const writer = ds.writable.getWriter();

        // 写入数据
        writer.write(data);
        writer.close();

        // 读取解压缩后的数据
        return new Promise<Uint8Array>((resolve, reject) => {
          const reader = ds.readable.getReader();
          const chunks: Uint8Array[] = [];

          function readChunk() {
            reader.read().then(({ done, value }) => {
              if (done) {
                // 合并所有块
                const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
                const result = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                  result.set(chunk, offset);
                  offset += chunk.length;
                }

                resolve(result);
              } else {
                chunks.push(value);
                readChunk();
              }
            }).catch(reject);
          }

          readChunk();
        });
      } catch (error) {
        Debug.warn('DataCompressor', 'Brotli解压缩失败，使用内置的简单实现:', error);
      }
    }

    // 如果不支持DecompressionStream，则尝试使用简单的方法
    try {
      const decoder = new TextDecoder();
      const jsonString = decoder.decode(data);

      if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
        return new TextEncoder().encode(jsonString);
      }
    } catch (error) {
      // 忽略错误
    }

    return data;
  }

  /**
   * 使用Deflate解压缩
   * @param data 要解压缩的数据
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  private async decompressWithDeflate(data: Uint8Array, _level: CompressionLevel): Promise<Uint8Array> {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果浏览器支持DecompressionStream，则使用DecompressionStream
    if (typeof DecompressionStream !== 'undefined') {
      try {
        // 创建解压缩流
        const ds = new DecompressionStream('deflate');
        const writer = ds.writable.getWriter();

        // 写入数据
        writer.write(data);
        writer.close();

        // 读取解压缩后的数据
        return new Promise<Uint8Array>((resolve, reject) => {
          const reader = ds.readable.getReader();
          const chunks: Uint8Array[] = [];

          function readChunk() {
            reader.read().then(({ done, value }) => {
              if (done) {
                // 合并所有块
                const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
                const result = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                  result.set(chunk, offset);
                  offset += chunk.length;
                }

                resolve(result);
              } else {
                chunks.push(value);
                readChunk();
              }
            }).catch(reject);
          }

          readChunk();
        });
      } catch (error) {
        Debug.warn('DataCompressor', 'Deflate解压缩失败，使用内置的简单实现:', error);
      }
    }

    // 如果不支持DecompressionStream，则尝试使用简单的方法
    try {
      const decoder = new TextDecoder();
      const jsonString = decoder.decode(data);

      if (jsonString.startsWith('{') || jsonString.startsWith('[')) {
        return new TextEncoder().encode(jsonString);
      }
    } catch (error) {
      // 忽略错误
    }

    return data;
  }

  /**
   * 使用字典解压缩
   * @param data 要解压缩的数据
   * @param dictionary 压缩字典
   * @returns 解压缩后的数据
   */
  private decompressWithDictionary(data: Uint8Array, dictionary: Uint8Array): Uint8Array {
    // 简单的字典解压缩实现
    // 在实际项目中，应该使用更高效的字典解压缩算法

    // 将字典分割为多个短语
    const phrases: Uint8Array[] = [];
    const minPhraseLength = 3;
    const maxPhraseLength = 32;

    for (let i = 0; i < dictionary.length - minPhraseLength; i++) {
      for (let length = minPhraseLength; length <= maxPhraseLength && i + length <= dictionary.length; length++) {
        phrases.push(dictionary.slice(i, i + length));
      }
    }

    // 对短语进行排序，优先使用长的短语
    phrases.sort((a, b) => b.length - a.length);

    // 解压缩数据
    const result: number[] = [];
    let i = 0;

    while (i < data.length) {
      // 检查是否是短语引用
      if (data[i] === 0xFF && i + 3 < data.length) {
        // 获取短语索引和长度
        const phraseIndex = (data[i + 1] << 8) | data[i + 2];
        // 注意：phraseLength 在这个简单实现中暂时不使用，但保留以备将来扩展
        // const phraseLength = data[i + 3];

        // 检查短语索引是否有效
        if (phraseIndex < phrases.length) {
          const phrase = phrases[phraseIndex];

          // 添加短语
          for (let j = 0; j < phrase.length; j++) {
            result.push(phrase[j]);
          }

          i += 4;
        } else {
          // 无效的短语索引，添加原始字节
          result.push(data[i]);
          i++;
        }
      } else {
        // 添加原始字节
        result.push(data[i]);
        i++;
      }
    }

    return new Uint8Array(result);
  }

  /**
   * 选择自适应压缩算法和级别
   * @param dataSize 数据大小
   * @param options 压缩选项
   */
  private selectAdaptiveCompression(dataSize: number, options: Required<CompressionOptions>): void {
    // 根据数据大小选择算法和级别
    if (dataSize < 1024) { // 小于1KB
      options.algorithm = CompressionAlgorithm.LZ_STRING;
      options.level = CompressionLevel.LOW;
    } else if (dataSize < 10 * 1024) { // 小于10KB
      options.algorithm = CompressionAlgorithm.LZ_STRING;
      options.level = CompressionLevel.MEDIUM;
    } else if (dataSize < 50 * 1024) { // 小于50KB
      options.algorithm = CompressionAlgorithm.MSGPACK;
      options.level = CompressionLevel.MEDIUM;
    } else if (dataSize < 100 * 1024) { // 小于100KB
      options.algorithm = CompressionAlgorithm.CBOR;
      options.level = CompressionLevel.MEDIUM;
    } else if (dataSize < 500 * 1024) { // 小于500KB
      options.algorithm = CompressionAlgorithm.BROTLI;
      options.level = CompressionLevel.MEDIUM;
    } else { // 大于500KB
      options.algorithm = CompressionAlgorithm.BROTLI;
      options.level = CompressionLevel.HIGH;
    }

    // 如果数据大小超过1MB，考虑使用增量压缩
    if (dataSize > 1024 * 1024 && options.incremental.enabled) {
      options.algorithm = CompressionAlgorithm.INCREMENTAL;
    }

    // 如果数据大小超过5MB，启用字典压缩
    if (dataSize > 5 * 1024 * 1024) {
      options.useDictionaryCompression = true;
    }
  }

  /**
   * 使用CBOR压缩
   * @param data 要压缩的数据
   * @param level 压缩级别
   * @returns 压缩后的数据
   */
  private compressWithCBOR(data: any, _level: CompressionLevel): Uint8Array {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果已加载CBOR库，则使用CBOR库
    if (this.compressionLibs.cbor) {
      try {
        return this.compressionLibs.cbor.encode(data);
      } catch (error) {
        Debug.warn('DataCompressor', 'CBOR压缩失败，使用内置的简单实现:', error);
      }
    }

    // 使用简单的实现
    return this.convertToBinary(data);
  }

  /**
   * 使用CBOR解压缩
   * @param data 要解压缩的数据
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  private decompressWithCBOR(data: Uint8Array, _level: CompressionLevel): any {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果已加载CBOR库，则使用CBOR库
    if (this.compressionLibs.cbor) {
      try {
        return this.compressionLibs.cbor.decode(data);
      } catch (error) {
        Debug.warn('DataCompressor', 'CBOR解压缩失败，使用内置的简单实现:', error);
      }
    }

    // 使用简单的实现
    const decoder = new TextDecoder();
    const jsonString = decoder.decode(data);

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      return jsonString;
    }
  }

  /**
   * 使用Brotli压缩
   * @param data 要压缩的数据
   * @param level 压缩级别
   * @returns 压缩后的数据
   */
  private async compressWithBrotli(data: Uint8Array, _level: CompressionLevel): Promise<Uint8Array> {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果浏览器支持CompressionStream，则使用CompressionStream
    if (typeof CompressionStream !== 'undefined') {
      try {
        // 创建压缩流
        const cs = new CompressionStream('gzip' as any); // 使用 gzip 作为 Brotli 的替代
        const writer = cs.writable.getWriter();

        // 写入数据
        writer.write(data);
        writer.close();

        // 读取压缩后的数据
        return new Promise<Uint8Array>((resolve, reject) => {
          const reader = cs.readable.getReader();
          const chunks: Uint8Array[] = [];

          function readChunk() {
            reader.read().then(({ done, value }) => {
              if (done) {
                // 合并所有块
                const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
                const result = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                  result.set(chunk, offset);
                  offset += chunk.length;
                }

                resolve(result);
              } else {
                chunks.push(value);
                readChunk();
              }
            }).catch(reject);
          }

          readChunk();
        });
      } catch (error) {
        Debug.warn('DataCompressor', 'Brotli压缩失败，使用内置的简单实现:', error);
      }
    }

    // 如果不支持CompressionStream，则使用简单的RLE压缩
    return this.compressWithRLE(data);
  }

  /**
   * 使用Deflate压缩
   * @param data 要压缩的数据
   * @param level 压缩级别
   * @returns 压缩后的数据
   */
  private async compressWithDeflate(data: Uint8Array, _level: CompressionLevel): Promise<Uint8Array> {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 如果浏览器支持CompressionStream，则使用CompressionStream
    if (typeof CompressionStream !== 'undefined') {
      try {
        // 创建压缩流
        const cs = new CompressionStream('deflate');
        const writer = cs.writable.getWriter();

        // 写入数据
        writer.write(data);
        writer.close();

        // 读取压缩后的数据
        return new Promise<Uint8Array>((resolve, reject) => {
          const reader = cs.readable.getReader();
          const chunks: Uint8Array[] = [];

          function readChunk() {
            reader.read().then(({ done, value }) => {
              if (done) {
                // 合并所有块
                const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
                const result = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                  result.set(chunk, offset);
                  offset += chunk.length;
                }

                resolve(result);
              } else {
                chunks.push(value);
                readChunk();
              }
            }).catch(reject);
          }

          readChunk();
        });
      } catch (error) {
        Debug.warn('DataCompressor', 'Deflate压缩失败，使用内置的简单实现:', error);
      }
    }

    // 如果不支持CompressionStream，则使用简单的RLE压缩
    return this.compressWithRLE(data);
  }

  /**
   * 使用RLE压缩
   * @param data 要压缩的数据
   * @returns 压缩后的数据
   */
  private compressWithRLE(data: Uint8Array): Uint8Array {
    if (!data || data.length < 2) {
      return data;
    }

    const result: number[] = [];
    let count = 1;
    let current = data[0];

    for (let i = 1; i < data.length; i++) {
      if (data[i] === current) {
        count++;
      } else {
        // 如果重复次数大于3，则使用RLE编码
        if (count > 3) {
          result.push(0); // 标记为RLE编码
          result.push(count);
          result.push(current);
        } else {
          // 否则直接添加原始数据
          for (let j = 0; j < count; j++) {
            result.push(current);
          }
        }

        current = data[i];
        count = 1;
      }
    }

    // 处理最后一组数据
    if (count > 3) {
      result.push(0); // 标记为RLE编码
      result.push(count);
      result.push(current);
    } else {
      for (let j = 0; j < count; j++) {
        result.push(current);
      }
    }

    return new Uint8Array(result);
  }

  /**
   * 使用字典压缩
   * @param data 要压缩的数据
   * @param dictionary 压缩字典
   * @returns 压缩后的数据
   */
  private compressWithDictionary(data: Uint8Array, dictionary: Uint8Array): Uint8Array {
    // 简单的字典压缩实现
    // 在实际项目中，应该使用更高效的字典压缩算法

    // 将字典分割为多个短语
    const phrases: Uint8Array[] = [];
    const minPhraseLength = 3;
    const maxPhraseLength = 32;

    for (let i = 0; i < dictionary.length - minPhraseLength; i++) {
      for (let length = minPhraseLength; length <= maxPhraseLength && i + length <= dictionary.length; length++) {
        phrases.push(dictionary.slice(i, i + length));
      }
    }

    // 对短语进行排序，优先使用长的短语
    phrases.sort((a, b) => b.length - a.length);

    // 压缩数据
    const result: number[] = [];
    let i = 0;

    while (i < data.length) {
      let matched = false;

      // 查找匹配的短语
      for (let j = 0; j < phrases.length; j++) {
        const phrase = phrases[j];

        if (i + phrase.length <= data.length) {
          let match = true;

          for (let k = 0; k < phrase.length; k++) {
            if (data[i + k] !== phrase[k]) {
              match = false;
              break;
            }
          }

          if (match) {
            // 添加短语索引和长度
            result.push(0xFF); // 标记为短语引用
            result.push(j >> 8); // 高8位
            result.push(j & 0xFF); // 低8位
            result.push(phrase.length);

            i += phrase.length;
            matched = true;
            break;
          }
        }
      }

      if (!matched) {
        // 添加原始字节
        result.push(data[i]);
        i++;
      }
    }

    return new Uint8Array(result);
  }

  /**
   * 使用LZ-String压缩
   * @param data 要压缩的数据
   * @param level 压缩级别
   * @returns 压缩后的数据
   */
  private compressWithLZString(data: string, _level: CompressionLevel): string {
    // 如果已加载LZ-String库，则使用LZ-String库
    if (this.compressionLibs.lzString) {
      try {
        return this.compressionLibs.lzString.compress(data);
      } catch (error) {
        Debug.warn('DataCompressor', 'LZ-String压缩失败，使用内置的简单实现:', error);
      }
    }

    // 使用简单的RLE压缩作为后备
    return this.simpleRLECompress(data);
  }

  /**
   * 使用LZ-String解压缩
   * @param data 要解压缩的数据
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  private decompressWithLZString(data: string, _level: CompressionLevel): string {
    // 如果已加载LZ-String库，则使用LZ-String库
    if (this.compressionLibs.lzString) {
      try {
        return this.compressionLibs.lzString.decompress(data);
      } catch (error) {
        Debug.warn('DataCompressor', 'LZ-String解压缩失败，使用内置的简单实现:', error);
      }
    }

    // 使用简单的RLE解压缩作为后备
    return this.simpleRLEDecompress(data);
  }

  /**
   * 使用MessagePack压缩
   * @param data 要压缩的数据
   * @param level 压缩级别
   * @returns 压缩后的数据
   */
  private compressWithMessagePack(data: any, _level: CompressionLevel): Uint8Array {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 在实际项目中，应该使用MessagePack库
    // 这里使用简单的模拟实现

    // 将数据转换为JSON字符串
    const jsonString = JSON.stringify(data);

    // 转换为Uint8Array
    const encoder = new TextEncoder();
    return encoder.encode(jsonString);
  }

  /**
   * 使用MessagePack解压缩
   * @param data 要解压缩的数据
   * @param level 压缩级别
   * @returns 解压缩后的数据
   */
  private decompressWithMessagePack(data: string | Uint8Array, _level: CompressionLevel): any {
    // 注意：_level 参数在当前实现中未使用，但保留以备将来扩展
    // 在实际项目中，应该使用MessagePack库
    // 这里使用简单的模拟实现

    // 如果是Uint8Array，则转换为字符串
    let jsonString: string;

    if (data instanceof Uint8Array) {
      const decoder = new TextDecoder();
      jsonString = decoder.decode(data);
    } else {
      jsonString = data;
    }

    // 解析JSON字符串
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      return jsonString;
    }
  }

  /**
   * 简单的RLE压缩
   * @param data 要压缩的数据
   * @returns 压缩后的数据
   */
  private simpleRLECompress(data: string): string {
    if (!data || data.length < 2) {
      return data;
    }

    let result = '';
    let count = 1;
    let current = data[0];

    for (let i = 1; i < data.length; i++) {
      if (data[i] === current) {
        count++;
      } else {
        result += (count > 3 ? `${count}${current}` : current.repeat(count));
        current = data[i];
        count = 1;
      }
    }

    result += (count > 3 ? `${count}${current}` : current.repeat(count));

    return result;
  }

  /**
   * 简单的RLE解压缩
   * @param data 要解压缩的数据
   * @returns 解压缩后的数据
   */
  private simpleRLEDecompress(data: string): string {
    if (!data || data.length < 2) {
      return data;
    }

    let result = '';
    let i = 0;

    while (i < data.length) {
      // 检查是否是数字
      const countStart = i;
      while (i < data.length && /\d/.test(data[i])) {
        i++;
      }

      if (i > countStart && i < data.length) {
        // 找到了数字和字符
        const count = parseInt(data.substring(countStart, i), 10);
        const char = data[i];
        result += char.repeat(count);
        i++;
      } else {
        // 没有找到数字，直接添加字符
        result += data[i];
        i++;
      }
    }

    return result;
  }

  /**
   * 获取压缩统计信息
   * @returns 统计信息
   */
  public getStats(): any {
    const { compressionCount, decompressionCount, totalOriginalSize, totalCompressedSize, totalCompressionTime, totalDecompressionTime } = this.stats;

    // 计算平均压缩比率
    const averageRatio = compressionCount > 0 ? totalCompressedSize / totalOriginalSize : 1;

    // 计算平均压缩时间
    const averageCompressionTime = compressionCount > 0 ? totalCompressionTime / compressionCount : 0;

    // 计算平均解压缩时间
    const averageDecompressionTime = decompressionCount > 0 ? totalDecompressionTime / decompressionCount : 0;

    // 计算节省的字节数
    const savedBytes = totalOriginalSize - totalCompressedSize;

    return {
      compressionCount,
      decompressionCount,
      totalOriginalSize,
      totalCompressedSize,
      totalCompressionTime,
      totalDecompressionTime,
      averageRatio,
      averageCompressionTime,
      averageDecompressionTime,
      savedBytes,
      savingsPercentage: compressionCount > 0 ? (savedBytes / totalOriginalSize) * 100 : 0,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      compressionCount: 0,
      decompressionCount: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalCompressionTime: 0,
      totalDecompressionTime: 0,
      incrementalCompressionCount: 0,
      incrementalSavedBytes: 0,
      adaptiveAlgorithmSwitchCount: 0
    };
  }

  /**
   * 设置压缩选项
   * @param options 压缩选项
   */
  public setOptions(options: Partial<CompressionOptions>): void {
    this.options = {
      ...this.options,
      ...options,
      // 确保增量选项被正确合并
      incremental: {
        ...this.options.incremental,
        ...(options.incremental || {})
      }
    } as Required<CompressionOptions>;
  }

  /**
   * 获取压缩选项
   * @returns 压缩选项
   */
  public getOptions(): Required<CompressionOptions> {
    return { ...this.options };
  }

}
